"use client";

import { useRef, useState, useEffect } from 'react';
import AnimatedFixedTitle from './AnimatedFixedTitle';
import ProcessCard from './ProcessCard';

const Process = () => {
  const sectionRef = useRef(null);
  const [hasAnimatedIn, setHasAnimatedIn] = useState(false);
  const [titleVisible, setTitleVisible] = useState(false);
  const [scrollEffects, setScrollEffects] = useState({
    opacity: 1,
    blur: 0,
    scale: 1
  });
  const [isProcessComplete, setIsProcessComplete] = useState(false);
  const [normalScrollProgress, setNormalScrollProgress] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      if (!sectionRef.current) return;

      const processRect = sectionRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;

      // Get Projects section (previous section) to determine when to show Process title
      const projectsSection = document.querySelector('[data-section="projects"]');
      if (!projectsSection) return;

      const projectsRect = projectsSection.getBoundingClientRect();
      const projectsBottom = projectsRect.bottom;
      const projectsTop = projectsRect.top;

      // Show title earlier - adjust this value to control when title disappears on scroll up
      // Higher values = disappears earlier, Lower values = disappears later
      const triggerPoint = windowHeight * 0.2; // Try 0.6 (60%) to make it disappear earlier
      const projectsAlmostGone = projectsBottom <= triggerPoint;
      const projectsStartedScrolling = projectsTop <= windowHeight * 0.8;

      // Title appears when Projects is almost gone - SAME LOGIC AS PROJECTS
      const shouldShowTitle = projectsAlmostGone && projectsStartedScrolling;

      // Show animation when scrolling down and condition is met
      if (shouldShowTitle && !hasAnimatedIn) {
        setHasAnimatedIn(true);
        setTitleVisible(true);
      }

      // Hide animation when scrolling back up past the same trigger point
      if (!shouldShowTitle && hasAnimatedIn) {
        setHasAnimatedIn(false);
        setTitleVisible(false);
      }

      // Calculate scroll-driven effects when title is visible
      if (titleVisible) {
        // Calculate how far we've scrolled into the Process section
        const processTop = processRect.top;

        // Start process animations when Process section reaches top of viewport
        const triggerOffset = windowHeight * 0.1;
        if (processTop <= triggerOffset) {
          // SIMPLE CALCULATION - Direct scroll mapping for natural speed
          const scrolledIntoProcess = Math.abs(processTop - triggerOffset);

          // Use FULL section height for 1:1 mapping with actual section size
          const sectionScrollDistance = windowHeight * 4.75; // Match the 475vh section height (includes 75vh delay)
          const rawScrollProgress = Math.min(1, scrolledIntoProcess / sectionScrollDistance);

          // NO DELAY BUFFER - direct scroll response for natural speed
          const scrollProgress = rawScrollProgress;

          // Apply scroll-driven effects to title
          const opacity = scrollProgress > 0 ? Math.max(0, 1 - (scrollProgress * 3)) : 1;
          const blur = scrollProgress * 10;
          const scale = scrollProgress > 0 ? Math.max(0.8, 1 - (scrollProgress * 0.5)) : 1;

          setScrollEffects({ opacity, blur, scale });

          // Simple completion - section ends when cards are done
          setIsProcessComplete(scrollProgress >= 1);
          setNormalScrollProgress(Math.max(0, scrollProgress - 1));
        } else {
          // Reset effects when Process section hasn't reached the trigger point yet
          setScrollEffects({ opacity: 1, blur: 0, scale: 1 });
          setIsProcessComplete(false);
          setNormalScrollProgress(0);
        }
      } else {
        // Title not visible yet - reset everything
        setScrollEffects({ opacity: 1, blur: 0, scale: 1 });
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    // Check initial position
    handleScroll();

    return () => window.removeEventListener('scroll', handleScroll);
  }, [hasAnimatedIn, titleVisible]);

  // Process steps data
  const processSteps = [
    {
      id: 1,
      number: "01",
      title: "Initial Proposal",
      description: "We discuss your vision and I provide a detailed proposal with timeline and pricing."
    },
    {
      id: 2,
      number: "02",
      title: "Design & Planning",
      description: "I create wireframes and mockups, then plan the technical architecture for your project."
    },
    {
      id: 3,
      number: "03",
      title: "Development",
      description: "I build your project using modern technologies, keeping you updated throughout the process."
    },
    {
      id: 4,
      number: "04",
      title: "Launch & Support",
      description: "I deploy your project and provide ongoing support to ensure everything runs smoothly."
    }
  ];

  return (
    <>
      {/* Fixed/Centered Title with scroll-driven effects - LOWER Z-INDEX */}
      <div
        className="fixed inset-0 flex items-center justify-center pointer-events-none z-0"
        style={{
          transform: isProcessComplete ? `translateY(${-normalScrollProgress * 100}vh)` : 'translateY(0)'
        }}
      >
        <AnimatedFixedTitle
          title="Process"
          titleVisible={titleVisible}
          scrollEffects={scrollEffects}
          className="font-heading font-extrabold text-secondary text-4xl lg:text-6xl"
          containerClassName=""
        />
      </div>

      {/* Process Section */}
      <section
        ref={sectionRef}
        data-section="process"
        className="bg-background relative z-10"
        style={{ height: '475vh' }} // 4.75x viewport height = 0.75vh delay + 4 steps (1vh each)
      >
        {/* Process Cards Container */}
        <div className="sticky top-0 h-screen w-full overflow-hidden">
          {/* Split Screen Process Layout */}
          <div className="h-full flex items-center justify-center relative bg-background">
            <ProcessCard
              processSteps={processSteps}
            />
          </div>
        </div>
      </section>
    </>
  );
};

export default Process;

"use client";

import { useEffect, useState } from 'react';

const ProcessCard = ({ processSteps }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [stepProgress, setStepProgress] = useState(0); // 0-1 for each step's animation

  useEffect(() => {
    const handleScroll = () => {
      if (typeof window === 'undefined') return;

      const windowHeight = window.innerHeight;
      const scrollY = window.scrollY;

      // Find process section
      const processSection = document.querySelector('[data-section="process"]');
      if (!processSection) return;

      const rect = processSection.getBoundingClientRect();
      const sectionTop = scrollY + rect.top;
      const scrollIntoSection = scrollY - sectionTop;

      // Calculate which step should be active and its progress
      const initialDelay = windowHeight * 0.75;
      const stepScrollDistance = windowHeight * 1; // Each step takes 1vh to complete its animation

      // Determine current step
      const totalScrolled = Math.max(0, scrollIntoSection - initialDelay);
      const stepIndex = Math.floor(totalScrolled / stepScrollDistance);
      const stepScrollProgress = (totalScrolled % stepScrollDistance) / stepScrollDistance;

      setCurrentStep(Math.min(stepIndex, processSteps.length - 1));
      setStepProgress(stepScrollProgress);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll();

    return () => window.removeEventListener('scroll', handleScroll);
  }, [processSteps]);

  // Calculate slot machine animation values
  const getSlotAnimation = (progress) => {
    // Slot machine effect: slide down (0-0.3), stay center (0.3-0.7), slide up (0.7-1)
    if (progress <= 0.3) {
      // Sliding down into view
      const slideProgress = progress / 0.3;
      return {
        translateY: -100 + (slideProgress * 100), // From -100% to 0%
        opacity: slideProgress
      };
    } else if (progress <= 0.7) {
      // Staying in center
      return {
        translateY: 0,
        opacity: 1
      };
    } else {
      // Sliding up out of view
      const slideProgress = (progress - 0.7) / 0.3;
      return {
        translateY: -(slideProgress * 100), // From 0% to -100%
        opacity: 1 - slideProgress
      };
    }
  };

  const currentAnimation = getSlotAnimation(stepProgress);
  const currentStepData = processSteps[currentStep];

  return (
    <div className="w-full h-full flex items-center justify-center">
      {/* Split Screen Layout */}
      <div className="w-full max-w-6xl mx-auto px-8 grid grid-cols-2 gap-16 items-center">

        {/* Left Side - Step Numbers/Icons */}
        <div className="flex flex-col items-center justify-center">
          <div className="relative overflow-hidden h-32 flex items-center justify-center">
            <div
              className="flex-shrink-0 w-24 h-24 bg-accent rounded-full flex items-center justify-center transition-all duration-200"
              style={{
                transform: `translateY(${currentAnimation.translateY}%)`,
                opacity: currentAnimation.opacity
              }}
            >
              <span className="text-primary text-3xl font-bold font-heading">
                {currentStepData?.number}
              </span>
            </div>
          </div>
        </div>

        {/* Right Side - Content */}
        <div className="flex flex-col justify-center">
          <div className="relative overflow-hidden min-h-[200px] flex flex-col justify-center">
            <div
              className="transition-all duration-200"
              style={{
                transform: `translateY(${currentAnimation.translateY}%)`,
                opacity: currentAnimation.opacity
              }}
            >
              {/* Step Title */}
              <h3 className="text-secondary font-heading font-bold text-3xl mb-6">
                {currentStepData?.title}
              </h3>

              {/* Step Description */}
              <p className="text-secondary/80 text-lg leading-relaxed">
                {currentStepData?.description}
              </p>
            </div>
          </div>
        </div>

      </div>
    </div>
  );
};

export default ProcessCard;
